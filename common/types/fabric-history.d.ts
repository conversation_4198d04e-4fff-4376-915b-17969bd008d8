// Type declarations for fabric-history package
declare module 'fabric-history' {
  // This module extends fabric.js with history functionality
}

declare module 'fabric' {
  namespace fabric {
    interface Canvas {
      // fabric-history methods
      undo(callback?: () => void): void;
      redo(callback?: () => void): void;
      clearHistory(): void;

      // fabric-history events
      on(eventName: 'history:append', handler: () => void): void;
      on(eventName: 'history:undo', handler: () => void): void;
      on(eventName: 'history:redo', handler: () => void): void;
      on(eventName: 'history:clear', handler: () => void): void;

      off(eventName: 'history:append', handler: () => void): void;
      off(eventName: 'history:undo', handler: () => void): void;
      off(eventName: 'history:redo', handler: () => void): void;
      off(eventName: 'history:clear', handler: () => void): void;
    }
  }
}

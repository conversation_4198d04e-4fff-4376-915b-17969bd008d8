'use client'

import React, {
  useState, useEffect,
} from 'react';
import { Canvas, FabricObject } from 'fabric';
import {
  X,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  ChevronUp,
  ChevronDown,
  Trash2,
  Type,
  Image as ImageIcon,
  Square,
  Circle,
} from 'lucide-react';

interface LayersPanelProps {
    canvas: Canvas | null;
    onClose: () => void;
}

interface LayerInfo {
    object: FabricObject;
    id: string;
    name: string;
    type: string;
    visible: boolean;
    locked: boolean;
    selected: boolean;
}

export const LayersPanel = ({ canvas, onClose }: LayersPanelProps) => {
  const [layers, setLayers] = useState<LayerInfo[]>([]);

  const getObjectIcon = (type: string) => {
    switch (type) {
      case 'text':
      case 'i-text':
        return <Type size={14} />;
      case 'image':
        return <ImageIcon size={14} />;
      case 'rect':
        return <Square size={14} />;
      case 'circle':
        return <Circle size={14} />;
      default:
        return <Square size={14} />;
    }
  };

  const getObjectName = (obj: FabricObject, index: number) => {
    if (obj.type === 'text' || obj.type === 'i-text') {
      const text = (obj as any).text || '';
      return text.length > 20 ? `${text.substring(0, 20)}...` : text || `Text ${index + 1}`;
    }
    if (obj.type === 'image') {
      return `Image ${index + 1}`;
    }
    return `${obj.type?.charAt(0).toUpperCase()}${obj.type?.slice(1)} ${index + 1}`;
  };

  const updateLayers = () => {
    if (!canvas) return;

    const objects = canvas.getObjects();
    const activeObjects = canvas.getActiveObjects();
    
    const layerInfos: LayerInfo[] = objects.map((obj, index) => ({
      object: obj,
      id: `layer-${index}`,
      name: getObjectName(obj, index),
      type: obj.type || 'unknown',
      visible: obj.visible !== false,
      locked: obj.selectable === false,
      selected: activeObjects.includes(obj),
    })).reverse(); // Reverse to show top layers first

    setLayers(layerInfos);
  };

  useEffect(() => {
    if (!canvas) return;

    updateLayers();

    const handleSelectionChange = () => updateLayers();
    const handleObjectChange = () => updateLayers();

    canvas.on('selection:created', handleSelectionChange);
    canvas.on('selection:updated', handleSelectionChange);
    canvas.on('selection:cleared', handleSelectionChange);
    canvas.on('object:added', handleObjectChange);
    canvas.on('object:removed', handleObjectChange);
    canvas.on('object:modified', handleObjectChange);

    return () => {
      canvas.off('selection:created', handleSelectionChange);
      canvas.off('selection:updated', handleSelectionChange);
      canvas.off('selection:cleared', handleSelectionChange);
      canvas.off('object:added', handleObjectChange);
      canvas.off('object:removed', handleObjectChange);
      canvas.off('object:modified', handleObjectChange);
    };
  }, [canvas]);

  const selectLayer = (layer: LayerInfo) => {
    if (!canvas) return;
    canvas.setActiveObject(layer.object);
    canvas.renderAll();
  };

  const toggleVisibility = (layer: LayerInfo) => {
    if (!canvas) return;
    layer.object.visible = !layer.object.visible;
    canvas.renderAll();
    updateLayers();
  };

  const toggleLock = (layer: LayerInfo) => {
    if (!canvas) return;
    layer.object.selectable = !layer.object.selectable;
    layer.object.evented = !layer.object.evented;
    canvas.renderAll();
    updateLayers();
  };

  const moveLayerUp = (layer: LayerInfo) => {
    if (!canvas) return;
    const objects = canvas.getObjects();
    const currentIndex = objects.indexOf(layer.object);
    
    if (currentIndex < objects.length - 1) {
      canvas.remove(layer.object);
      canvas.insertAt(currentIndex + 1, layer.object);
      canvas.renderAll();
      updateLayers();
    }
  };

  const moveLayerDown = (layer: LayerInfo) => {
    if (!canvas) return;
    const objects = canvas.getObjects();
    const currentIndex = objects.indexOf(layer.object);
    
    if (currentIndex > 0) {
      canvas.remove(layer.object);
      canvas.insertAt(currentIndex - 1, layer.object);
      canvas.renderAll();
      updateLayers();
    }
  };

  const deleteLayer = (layer: LayerInfo) => {
    if (!canvas) return;
    canvas.remove(layer.object);
    canvas.renderAll();
    updateLayers();
  };

  return (
    <div className="fixed bottom-6 right-24 z-40 w-80 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <h3 className="font-medium text-gray-900">Layers</h3>
        <button
          onClick={onClose}
          className="p-1 rounded hover:bg-gray-100 transition-colors"
        >
          <X size={16} className="text-gray-500" />
        </button>
      </div>

      {/* Layers List */}
      <div className="max-h-96 overflow-y-auto">
        {layers.length === 0 ? (
          <div className="p-4 text-center text-gray-500 text-sm">
            No layers yet. Add some content to see layers here.
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {layers.map((layer) => (
              <div
                key={layer.id}
                className={`flex items-center gap-2 p-2 rounded cursor-pointer transition-colors ${
                  layer.selected
                    ? 'bg-blue-100 border border-blue-300'
                    : 'hover:bg-gray-50 border border-transparent'
                }`}
                onClick={() => selectLayer(layer)}
              >
                {/* Object Icon */}
                <div className="text-gray-600">
                  {getObjectIcon(layer.type)}
                </div>

                {/* Layer Name */}
                <div className="flex-1 text-sm text-gray-900 truncate">
                  {layer.name}
                </div>

                {/* Controls */}
                <div className="flex items-center gap-1">
                  {/* Move Up */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      moveLayerUp(layer);
                    }}
                    className="p-1 rounded hover:bg-gray-200 transition-colors"
                    title="Move Up"
                  >
                    <ChevronUp size={12} className="text-gray-500" />
                  </button>

                  {/* Move Down */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      moveLayerDown(layer);
                    }}
                    className="p-1 rounded hover:bg-gray-200 transition-colors"
                    title="Move Down"
                  >
                    <ChevronDown size={12} className="text-gray-500" />
                  </button>

                  {/* Visibility Toggle */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleVisibility(layer);
                    }}
                    className="p-1 rounded hover:bg-gray-200 transition-colors"
                    title={layer.visible ? 'Hide' : 'Show'}
                  >
                    {layer.visible ? (
                      <Eye size={12} className="text-gray-500" />
                    ) : (
                      <EyeOff size={12} className="text-gray-400" />
                    )}
                  </button>

                  {/* Lock Toggle */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleLock(layer);
                    }}
                    className="p-1 rounded hover:bg-gray-200 transition-colors"
                    title={layer.locked ? 'Unlock' : 'Lock'}
                  >
                    {layer.locked ? (
                      <Lock size={12} className="text-gray-500" />
                    ) : (
                      <Unlock size={12} className="text-gray-400" />
                    )}
                  </button>

                  {/* Delete */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteLayer(layer);
                    }}
                    className="p-1 rounded hover:bg-red-100 transition-colors"
                    title="Delete"
                  >
                    <Trash2 size={12} className="text-red-500" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

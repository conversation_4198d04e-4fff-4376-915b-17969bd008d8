'use client'

import React, {
  useEffect, useState,
} from 'react';
import { Canvas } from 'fabric';
import {
  Wand2,
  Upload,
  Type,
  Clock,
  LucideProps,
  Sparkles,
  Shapes,
  X,
  DoorClosed,
} from 'lucide-react';
import { GenerateImagePanel } from './tabs/GenerateImagePanel';
import { ImproveImagePanel } from './tabs/ImproveImagePanel';
import { UploadImagePanel } from './tabs/UploadImagePanel';
import { TextPanel } from './tabs/TextPanel';
import { RecentUploadsPanel } from './tabs/RecentUploadsPanel';
import { VectorImagePanel } from './tabs/VectorImagePanel';

interface CanvasSidebarProps {
  canvas: Canvas | null;
  agentId: string;
  planId: string;
  containerRef?: React.RefObject<HTMLDivElement>;
  zoomLevel?: number;
  onClose?: () => void;
}

interface SidebarTab {
  id: string;
  icon: React.ForwardRefExoticComponent<Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>>;
  label: string;
  content: React.ComponentType<{
    canvas: Canvas | null;
    agentId?: string;
    planId?: string;
    containerRef?: React.RefObject<HTMLDivElement>;
    zoomLevel?: number;
  }>;
}

export const CanvasSidebar = ({
  canvas, agentId, planId, containerRef, zoomLevel, onClose,
}: CanvasSidebarProps) => {
  const [activeTab, setActiveTab] = useState('generate');
  const [hasCanvasContent, setHasCanvasContent] = useState(false);

  useEffect(() => {
    const checkCanvasContent = () => {
      if (canvas) {
        const objects = canvas.getObjects();
        setHasCanvasContent(objects.length > 0);
      } else {
        setHasCanvasContent(false);
      }
    };

    checkCanvasContent();

    if (canvas) {
      canvas.on('object:added', checkCanvasContent);
      canvas.on('object:removed', checkCanvasContent);
      canvas.on('canvas:cleared', checkCanvasContent);

      return () => {
        canvas.off('object:added', checkCanvasContent);
        canvas.off('object:removed', checkCanvasContent);
        canvas.off('canvas:cleared', checkCanvasContent);
      };
    }
  }, [canvas]);

  const tabs: SidebarTab[] = [
    {
      id: "generate",
      icon: Wand2,
      label: "Generate",
      content: GenerateImagePanel,
    },
    {
      id: "vector",
      icon: Shapes,
      label: "Vector",
      content: VectorImagePanel,
    },
    {
      id: "improve",
      icon: Sparkles,
      label: "Improve",
      content: ImproveImagePanel,
    },
    {
      id: "upload",
      icon: Upload,
      label: "Import",
      content: UploadImagePanel,
    },
    {
      id: "text",
      icon: Type,
      label: "Text",
      content: TextPanel,
    },
    {
      id: "recent",
      icon: Clock,
      label: "Recent",
      content: RecentUploadsPanel,
    },
  ];

  const closeButton = {
    id: "close",
    icon: X,
    label: "Close",
  };

  const ActiveContent = tabs.find(tab => tab.id === activeTab)?.content || GenerateImagePanel;

  return (
    <div className="w-[480px] bg-neutral-900 border-r border-neutral-700 flex h-full">
      <div className="w-20 bg-neutral-800 flex flex-col border-r border-neutral-700">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isImproveTab = tab.id === 'improve';
          const isDisabled = isImproveTab && !hasCanvasContent;

          return (
            <button
              key={tab.id}
              onClick={() => !isDisabled && setActiveTab(tab.id)}
              disabled={isDisabled}
              className={`p-4 flex flex-col items-center gap-1 transition-all duration-200 ${
                isDisabled
                  ? 'text-gray-600 cursor-not-allowed opacity-50'
                  : activeTab === tab.id
                    ? 'bg-gradient-to-tr from-violets-are-blue to-han-purple text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-neutral-700'
              }`}
              title={isDisabled ? 'Add content to canvas first' : tab.label}
            >
              <Icon size={18} />
              <span className="text-xs font-medium">{tab.label}</span>
            </button>
          );
        })}

        <div className="flex-1" />
        {onClose && (
          <button
            onClick={onClose}
            className="p-4 flex flex-col items-center gap-1 transition-all duration-200 text-gray-400 hover:text-white hover:bg-neutral-700 border-t border-neutral-700"
            title="Close Editor"
          >
            <DoorClosed size={18} />
            <span className="text-xs font-medium">{closeButton.label}</span>
          </button>
        )}
      </div>

      <div className="flex-1 overflow-y-auto bg-neutral-900">
        <div className="h-full">
          <ActiveContent
            canvas={canvas}
            agentId={agentId}
            planId={planId}
            containerRef={containerRef}
            zoomLevel={zoomLevel}
          />
        </div>
      </div>
    </div>
  );
};

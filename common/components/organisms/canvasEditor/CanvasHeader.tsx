'use client'

import React, {
  useState, useEffect,
} from 'react';
import * as fabric from 'fabric';
import { Button } from '@/common/components/atoms';
import { motion } from "framer-motion";
import Link from 'next/link';
import { routes } from '@/common/routes';
import { ClientLogo } from '../header/ClientLogo';
import { secondaryFont } from '@/common/utils/localFont';

interface CanvasWithV6Methods extends fabric.Canvas {
  insertAt: (index: number, ...objects: fabric.Object[]) => number;
}

interface CanvasHeaderProps {
  onSaveDesign: () => void;
  canvas?: fabric.Canvas | null;
}

export const CanvasHeader = ({
  onSaveDesign,
  canvas,
}: CanvasHeaderProps) => {
  const [selectedObject, setSelectedObject] = useState<fabric.Object | null>(null);
  const [canMoveUp, setCanMoveUp] = useState(false);
  const [canMoveDown, setCanMoveDown] = useState(false);

  useEffect(() => {
    if (!canvas) {
      return;
    }

    const updateSelection = () => {
      const activeObject = canvas.getActiveObject();
      setSelectedObject(activeObject || null);

      if (activeObject) {
        const objects = canvas.getObjects();
        const currentIndex = objects.indexOf(activeObject);
        setCanMoveUp(currentIndex < objects.length - 1);
        setCanMoveDown(currentIndex > 0);
      } else {
        setCanMoveUp(false);
        setCanMoveDown(false);
      }
    };

    canvas.on('selection:created', updateSelection);
    canvas.on('selection:updated', updateSelection);
    canvas.on('selection:cleared', updateSelection);
    canvas.on('object:added', updateSelection);
    canvas.on('object:removed', updateSelection);

    return () => {
      canvas.off('selection:created', updateSelection);
      canvas.off('selection:updated', updateSelection);
      canvas.off('selection:cleared', updateSelection);
      canvas.off('object:added', updateSelection);
      canvas.off('object:removed', updateSelection);
    };
  }, [canvas]);

  const moveLayerUp = () => {
    if (!canvas || !selectedObject) {
      return;
    }

    const objects = canvas.getObjects();
    const currentIndex = objects.indexOf(selectedObject);

    if (currentIndex < objects.length - 1) {
      canvas.remove(selectedObject);
      (canvas as CanvasWithV6Methods).insertAt(currentIndex + 1, selectedObject);
      canvas.renderAll();
    }

    const newObjects = canvas.getObjects();
    const newIndex = newObjects.indexOf(selectedObject);
    setCanMoveUp(newIndex < newObjects.length - 1);
    setCanMoveDown(newIndex > 0);
  };

  const moveLayerDown = () => {
    if (!canvas || !selectedObject) {
      return;
    }

    const objects = canvas.getObjects();
    const currentIndex = objects.indexOf(selectedObject);

    if (currentIndex > 0) {
      canvas.remove(selectedObject);
      (canvas as CanvasWithV6Methods).insertAt(currentIndex - 1, selectedObject);
      canvas.renderAll();
    }

    const newObjects = canvas.getObjects();
    const newIndex = newObjects.indexOf(selectedObject);
    setCanMoveUp(newIndex < newObjects.length - 1);
    setCanMoveDown(newIndex > 0);
  };

  return (
    <div className="bg-eerie-black border-b border-neutral-700 px-4 py-2 md:py-3 flex items-center justify-between relative">
      <div className="flex items-center gap-1 md:gap-2">
        <Link href={routes.homePath} prefetch={true} replace className="flex gap-1 md:gap-2 items-center text-white font-semibold text-xl md:text-2xl">
          <ClientLogo width={24} height={24}/>
          <motion.span
            initial={{
              opacity: 0,
              translateX: 20,
            }}
            animate={{
              opacity: 1,
              translateX: 0,
            }}
            transition={{
              duration: 0.5,
              delay: 1,
            }}
            className={`text-white ${secondaryFont.className} animate-glowTransition`}
          >
            Media Pilot
          </motion.span>
        </Link>
        {selectedObject && (
          <>
            <Button
              onClick={moveLayerUp}
              disabled={!canMoveUp}
              variant="outline"
              size="sm"
              title="Bring to Front"
            >
              Bring to Front
            </Button>
            <Button
              onClick={moveLayerDown}
              disabled={!canMoveDown}
              variant="outline"
              size="sm"
              title="Send Back"
            >
              Send Back
            </Button>
          </>
        )}
      </div>
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="gradient"
          size="sm"
          onClick={onSaveDesign}
        >
          Save to Post
        </Button>
      </div>
    </div>
  );
};
